#!/usr/bin/env python3
"""
CCF A类会议的DBLP查询规则和特殊处理规则
用于统一管理各个会议在DBLP中的venue名称映射和特殊处理逻辑

⚠️ 重要踩坑记录：

1. 【DBLP API点号问题】
   - 查询时使用无点号版本：'Proc ACM Program Lang'
   - 过滤时使用带点号版本：'Proc. ACM Program. Lang.'
   - 原因：DBLP API查询和返回结果的venue名称格式不一致

2. 【期刊发布模式】
   - OOPSLA/PLDI/POPL等会议从2017年开始论文发布在PACMPL期刊中
   - 不能直接查询会议名，需要查询期刊名然后过滤
   - 需要在DBLP网站确认具体的发布模式

3. 【Venue名称精确匹配】
   - 很多会议有companion、workshop等相关论文
   - 必须使用精确的venue名称过滤，避免获取到非主会议论文
   - 例如：CSCW vs CSCW Companion, FSE vs SIGSOFT FSE Companion

4. 【简化名称优于全名】
   - IEEE S&P: 使用'SP'而不是'IEEE Symposium on Security and Privacy'
   - IEEE VIS: 使用'IEEE VIS'而不是'IEEE Visualization'
   - 原因：DBLP中实际使用的是简化名称

5. 【年份相关的venue变化】
   - 有些会议的venue名称在不同年份可能有变化
   - 需要通过DBLP网站确认具体年份的venue名称
   - 建议先在DBLP网站手动搜索确认再配置
"""


# 特殊处理规则：某些会议需要特殊的查询或过滤逻辑
CCF_A_CONFERENCES_SPECIAL_RULES = {
    'default' : {
        'query_venue': str,  # 查询 venue 名称
        'filter_venues': list,  # 过滤 venue 名称
        'filter_logic': str,  # 过滤逻辑
        'note': str,  # 备注
        'venue_alternatives': list,  # venue 名称的替代
        'year_rule': dict,  # 年份规则
    },

    # SIGMOD: 主会议论文发布在PACMMOD期刊，需要特殊处理
    'sigmod': {
        'query_venue': 'Proc ACM Manag Data',  # 查询时使用无点号
        'filter_venues': ['Proc. ACM Manag. Data'],  # 过滤时匹配带点号
        'venue_alternatives': [
            'Proc. ACM Manag. Data',  # 主会议论文
            'SIGMOD Conference Companion'  # 伴随会议论文
        ],
        'filter_logic': 'include_all',  # 包含所有匹配的venue
        'note': 'SIGMOD主会议论文现在发布在PACMMOD期刊中，查询和过滤使用不同格式'
    },

    # VLDB: 期刊形式，venue名称包含点号
    'vldb': {
        'query_venue': 'Proc VLDB Endow',  # 查询时使用无点号
        'filter_venues': ['Proc. VLDB Endow.'],  # 过滤时匹配带点号
        'venue_alternatives': [
            'Proc. VLDB Endow.',
            'VLDB J.'  # VLDB Journal的另一种表示
        ],
        'filter_logic': 'include_all',
        'note': 'VLDB以期刊形式发布，查询和过滤使用不同格式'
    },

    # NeurIPS: 2020年后改名
    'neurips': {
        'venue_alternatives': [
            'NeurIPS',
            'NIPS'  # 2020年前的名称
        ],
        'year_rule': {
            'before_2020': 'NIPS',
            'from_2020': 'NeurIPS'
        },
        'filter_logic': 'year_based',
        'note': '2020年从NIPS改名为NeurIPS'
    },

    # USENIX Security: 多种表示方式
    'usenix_security': {
        'venue_alternatives': [
            'USENIX Security',
            'USENIX Security Symposium'
        ],
        'filter_logic': 'include_all',
        'note': 'USENIX Security有多种venue表示方式'
    },

    # IEEE S&P: 特殊字符处理
    # ⚠️ 踩坑案例：使用全名'IEEE Symposium on Security and Privacy'无结果
    # ⚠️ 解决方案：使用DBLP中实际的简化名称'SP'
    'sp': {
        'query_venue': 'SP',  # 使用简化名称查询，不是全名
        'filter_venues': ['SP'],  # 过滤时也使用简化名称
        'filter_logic': 'include_all',
        'note': 'IEEE S&P必须使用简化名称SP，全名无效'
    },

    # OOPSLA: 论文发布在PACMPL期刊中
    # ⚠️ 踩坑案例：直接查询'OOPSLA'无结果，需要查询期刊名
    # ⚠️ 点号问题：查询用无点号，过滤用带点号
    'oopsla': {
        'query_venue': 'Proc ACM Program Lang',  # 查询PACMPL期刊（无点号）
        'filter_venues': ['Proc. ACM Program. Lang.'],  # 过滤时匹配带点号版本
        'filter_logic': 'include_all',
        'note': 'OOPSLA论文从2017年开始发布在PACMPL期刊中，存在点号格式问题'
    },

    # PLDI: 论文发布在PACMPL期刊中
    'pldi': {
        'query_venue': 'Proc ACM Program Lang',  # 查询PACMPL期刊
        'filter_venues': ['Proc. ACM Program. Lang.'],  # 过滤时匹配带点号
        'filter_logic': 'include_all',
        'note': 'PLDI论文发布在PACMPL期刊中'
    },

    # POPL: 论文发布在PACMPL期刊中
    'popl': {
        'query_venue': 'Proc ACM Program Lang',  # 查询PACMPL期刊
        'filter_venues': ['Proc. ACM Program. Lang.'],  # 过滤时匹配带点号
        'filter_logic': 'include_all',
        'note': 'POPL论文发布在PACMPL期刊中'
    },

    # CSCW: 论文发布在PACMHCI期刊中
    'cscw': {
        'query_venue': 'Proc ACM Hum Comput Interact',  # 查询PACMHCI期刊（无点号）
        'filter_venues': ['Proc. ACM Hum. Comput. Interact.'],  # 过滤时匹配带点号版本
        'filter_logic': 'include_all',
        'note': 'CSCW论文发布在PACMHCI期刊中，存在点号格式问题'
    },

    # UbiComp: 论文发布在IMWUT期刊中
    'ubicomp': {
        'query_venue': 'Proc ACM Interact Mob Wearable Ubiquitous Technol',  # 查询IMWUT期刊（无点号）
        'filter_venues': ['Proc. ACM Interact. Mob. Wearable Ubiquitous Technol.'],  # 过滤时匹配带点号版本
        'filter_logic': 'include_all',
        'note': 'UbiComp论文发布在IMWUT期刊中，存在点号格式问题'
    },

    # USENIX Security: 需要过滤出主会议论文，排除SOUPS等workshop
    'usenix_security': {
        'query_venue': 'USENIX Security',  # 查询USENIX Security
        'filter_venues': ['USENIX Security Symposium'],  # 只保留主会议论文
        'filter_logic': 'include_all',
        'note': 'USENIX Security需要过滤出主会议论文，排除SOUPS等workshop'
    },

    # USS: 与USENIX Security相同
    'uss': {
        'query_venue': 'USENIX Security',  # 查询USENIX Security
        'filter_venues': ['USENIX Security Symposium'],  # 只保留主会议论文
        'filter_logic': 'include_all',
        'note': 'USS与USENIX Security相同，需要过滤出主会议论文'
    },

    # FSE: 论文发布在PACMSE期刊中
    'fse': {
        'query_venue': 'Proc ACM Softw Eng',  # 查询PACMSE期刊（无点号）
        'filter_venues': ['Proc. ACM Softw. Eng.'],  # 过滤时匹配带点号版本
        'filter_logic': 'include_all',
        'note': 'FSE 2024论文发布在PACMSE期刊中，存在点号格式问题'
    },

    # ESEC: 与FSE相同，论文发布在PACMSE期刊中
    'esec': {
        'query_venue': 'Proc ACM Softw Eng',  # 查询PACMSE期刊（无点号）
        'filter_venues': ['Proc. ACM Softw. Eng.'],  # 过滤时匹配带点号版本
        'filter_logic': 'include_all',
        'note': 'ESEC与FSE相同，2024论文发布在PACMSE期刊中'
    },

    # WINE: 2024年论文数量很少，可能没有正常举办
    'wine': {
        'query_venue': 'WINE',  # 查询WINE
        'filter_venues': ['WINE'],  # 过滤WINE会议论文
        'filter_logic': 'include_all',
        'note': 'WINE 2024论文数量很少，可能没有正常举办或延期'
    },

    # ICCV: 2024年可能还未在DBLP中更新，或使用不同venue名称
    'iccv': {
        'query_venue': 'ICCV',  # 查询ICCV
        'filter_venues': ['ICCV'],  # 需要排除ICCVIT等其他会议
        'filter_logic': 'exclude',
        'exclude_venues': ['ICCVIT', 'ICCV Workshops'],
        'note': 'ICCV 2024可能还未在DBLP中更新，需要排除ICCVIT等其他会议'
    },

    # SC: Supercomputing会议，需要过滤出主会议论文
    'sc': {
        'query_venue': 'SC',  # 查询SC
        'filter_venues': ['SC'],  # 需要进一步确定主会议venue名称
        'filter_logic': 'include_all',
        'note': 'SC Supercomputing会议，需要过滤出主会议论文'
    },

    # VR: IEEE VR会议，需要过滤出主会议论文
    'vr': {
        'query_venue': 'VR',  # 查询VR
        'filter_venues': ['VR'],  # 需要进一步确定主会议venue名称
        'filter_logic': 'include_all',
        'note': 'IEEE VR会议，需要过滤出主会议论文'
    }
}


def get_special_rules(conference_key: str) -> dict:
    """ 获取会议的特殊处理规则 """
    return CCF_A_CONFERENCES_SPECIAL_RULES.get(conference_key.lower(), {})

def apply_year_based_venue_rule(conference_key: str, year: int) -> str:
    """ 根据年份应用特殊的venue规则 """
    rules = get_special_rules(conference_key)
    if 'year_rule' in rules:
        year_rule = rules['year_rule']
        if year < 2020 and 'before_2020' in year_rule:
            return year_rule['before_2020']
        elif year >= 2020 and 'from_2020' in year_rule:
            return year_rule['from_2020']

    return get_venue_name(conference_key)