import openreview.api

# 初始化 API 2 客户端（公开数据无需登录）
client = openreview.api.OpenReviewClient(baseurl='https://api2.openreview.net')

# 指定 forum ID
forum_id = '2NpAw2QJBY'

try:
    # 获取论文的 Note 对象
    note = client.get_note(id=forum_id)

    # 提取标题和摘要
    title = note.content.get('title', {}).get('value', 'N/A')
    abstract = note.content.get('abstract', {}).get('value', 'N/A')

    # 输出结果
    print(f"Title: {title}")
    print(f"Abstract: {abstract}")

except Exception as e:
    print(f"Error: {e}")