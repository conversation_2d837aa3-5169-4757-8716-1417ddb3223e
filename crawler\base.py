
import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)

from abc import ABC, abstractmethod
from typing import List, Dict, Optional
import logging
import json
import pandas as pd

OUTPUT_DIR = os.path.join(ROOT_DIR, 'results')

from utils.utils import DriverManager, WireDriverManager

class BaseCrawler(ABC):
    """论文爬虫基础类"""

    def __init__(self, target_year: Optional[int] = None, target_source: Optional[str] = None, use_wire_driver: bool = True, use_proxy: bool = True):
        self.target_year = target_year
        self.target_source = target_source
        self.papers_data = []
        self.papers_links = []

        # 选择驱动管理器类型
        if use_wire_driver:
            self.driver_manager = WireDriverManager(use_proxy=use_proxy)
            logging.info("使用selenium-wire驱动管理器（支持代理认证）")
        else:
            self.driver_manager = DriverManager(use_proxy=use_proxy)
            logging.info("使用标准驱动管理器")

        self.driver_manager.create_driver()
        
        self.save_link_dir = os.path.join(OUTPUT_DIR, 'links') # 保存链接目录
        self.save_paper_dir = os.path.join(OUTPUT_DIR, 'papers') # 保存论文目录
        self.filename_base = f"{self.target_source}_{self.target_year}" # 保存文件名

        os.makedirs(self.save_link_dir, exist_ok=True)
        os.makedirs(self.save_paper_dir, exist_ok=True)

    @abstractmethod
    def _get_paper_links(self) -> List[str]:
        """获取论文链接列表"""
        pass

    @abstractmethod
    def _crawl_paper_details(self, paper_url: str) -> Optional[Dict]:
        """爬取单篇论文详细信息"""
        pass

    def _get_filted_paper_links(self, paper_links: List[str]) -> List[str]:
        """过滤掉在之前保存的文件中已经得到摘要的论文链接"""
        current_papers_data = self._load_existing_paper_data()

        for paper_data in current_papers_data:
            if paper_data.get('abstract'):
                paper_links.remove(paper_data.get('url')) # 删除已经得到摘要的论文链接，只保留未得到摘要的论文链接
        
        logging.info(f"过滤掉 {len(current_papers_data)} 篇已经得到摘要的论文，保留 {len(paper_links)} 篇未得到摘要的论文")

        return paper_links

    def start_crawling(self, max_papers: int = 10, just_get_links: bool = False, just_get_paper_data: bool = False):
        """开始爬取论文 - 主要爬取流程"""
        logging.info(f"开始爬取论文，目标数量: {max_papers}")

        try:
            # 获取论文链接
            if just_get_links:
                self.papers_links = self._get_paper_links()
                self._save_data()
            else:
                self.papers_links = self._load_existing_paper_links() # 加载现有论文链接

            # 按max_papers限制爬取
            paper_links = self.papers_links[:max_papers]

            # 输出总体统计信息
            logging.info("=" * 60)
            logging.info(f"📋 论文链接获取完成 - 总体统计信息:")
            logging.info(f"   🔗 获取到论文链接: {len(paper_links)} 篇")
            logging.info(f"   📅 目标年份: {self.target_year}")
            logging.info(f"   🎯 目标出版商: {self.target_source}")
            logging.info(f"   🎯 计划爬取: {len(paper_links)} 篇")
            logging.info(f"   🎯 是否只获取论文链接: {just_get_links}")
            logging.info(f"   🎯 是否只获取论文数据: {just_get_paper_data}")
            logging.info("=" * 60)

            if not paper_links:
                logging.warning("⚠️  没有找到任何论文链接，请检查年份设置或网络连接")
                return
            if just_get_links:
                return

            # 爬取每篇论文的详细信息
            logging.info("🚀 开始逐篇爬取论文详细信息...")
            for i, paper_url in enumerate(paper_links, 1):
                paper_data = self._crawl_paper_details(paper_url)
                if paper_data:
                    self.papers_data.append(paper_data)
                    logging.info(f"   ✅ {i}/{len(paper_links)}: {paper_data.get('title', 'Unknown')[:50]}...")

            # 保存数据
            self._save_data() # 保存论文数据

        except Exception as e:
            logging.error(f"爬取过程中出现错误: {e}")
            raise
        finally:
            if self.driver_manager:
                self.driver_manager.quit()

    def get_stats(self) -> Dict:
        """获取爬取结果统计信息"""
        logging.info(f"📋 爬取结果统计信息:")
        logging.info(f"   🔗 带摘要的论文: {len([p for p in self.papers_data if p.get('abstract')])} 篇")
        logging.info(f"   🔗 带pdf的论文: {len([p for p in self.papers_data if p.get('pdf_url')])} 篇")
        logging.info(f"   🔗 带doi的论文: {len([p for p in self.papers_data if p.get('doi')])} 篇")

        return {
            'total_papers': len(self.papers_data),
            'papers_with_abstract': len([p for p in self.papers_data if p.get('abstract')]),
            'papers_with_pdf': len([p for p in self.papers_data if p.get('pdf_url')]),
            'papers_with_doi': len([p for p in self.papers_data if p.get('doi')])
        }

    def _clean_abstract_text(self, abstract: str) -> str:
        """清理摘要文本 """
        if not abstract:
            return ""
        cleaned = abstract.strip()
        if cleaned.startswith("Abstract"):
            cleaned = cleaned[8:].strip()
        import re
        cleaned = re.sub(r'\s+', ' ', cleaned)
        return cleaned.strip()

    def _load_existing_paper_data(self) -> List[Dict]:
        """加载现有数据"""
        json_path = os.path.join(self.save_paper_dir, f'{self.filename_base}.json')
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []

    def _load_existing_paper_links(self) -> List[str]:
        """加载现有论文链接"""
        json_path = os.path.join(self.save_link_dir, f'{self.filename_base}.json')
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []

    def _save_data(self):
        """保存爬取的数据，增量保存"""

        if not self.papers_links and not self.papers_data:
            logging.warning("没有数据需要保存")
            return

        # 1. 保存论文链接
        if self.papers_links:
            # 1.1 加载现有数据并合并
            current_papers_links = self._load_existing_paper_links()
            merged_papers_links = list(set(current_papers_links + self.papers_links))
            # 1.2 保存为JSON
            json_path = os.path.join(self.save_link_dir, f'{self.filename_base}.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(merged_papers_links, f, ensure_ascii=False, indent=2)
            logging.info(f"论文链接已保存到: {json_path}")

        # 2. 保存论文数据
        if self.papers_data:
            # 2.1 加载现有数据
            current_papers_data = self._load_existing_paper_data()
            current_papers_data_dict = {paper['url']: paper for paper in current_papers_data}

            # 2.2 按照 url 作为唯一标识对每个 item 进行合并
            for paper in self.papers_data:
                if paper.get('url') in current_papers_data_dict:
                    current_papers_data_dict[paper.get('url')].update(paper)
                else:
                    current_papers_data_dict[paper.get('url')] = paper
            merged_papers_data = list(current_papers_data_dict.values())

            # 2.1 保存为JSON
            json_path = os.path.join(self.save_paper_dir, f'{self.filename_base}.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(merged_papers_data, f, ensure_ascii=False, indent=2)
            logging.info(f"JSON数据已保存到: {json_path}")

            # 2.2 保存为CSV
            csv_path = os.path.join(self.save_paper_dir, f'{self.filename_base}.csv')
            df = pd.DataFrame(merged_papers_data)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            logging.info(f"CSV数据已保存到: {csv_path}")