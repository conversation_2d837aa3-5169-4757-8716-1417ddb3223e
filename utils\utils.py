import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

import time
import random
import logging
import requests
from typing import Dict, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.env.local')

import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from seleniumwire import webdriver as wire_webdriver


DRIVER_PATH = os.path.join(ROOT_DIR, 'crawler', 'undetected_chromedriver.exe')

# 配置
PROXY_API_CONFIG = {
    'url': 'http://api.shenlongip.com/ip',
    'params': {
        'key': os.getenv('PROXY_API_KEY'),
        'protocol': 1,  # 1=HTTP, 2=HTTPS - 统一使用HTTP
        'mr': 1,
        'pattern': 'json',
        'need': 1000,
        'count': 1,
        'sign': os.getenv('PROXY_API_SIGN')
    },
    'min_interval': 10,  # API调用最小间隔（秒）
    'proxy_lifetime': 300  # 代理生命周期（秒），5分钟后强制更换
}


class ProxyManager:
    """HTTP代理管理器"""

    def __init__(self):
        self.current_proxy = None
        self.proxy_start_time = None    # 代理开始使用时间

        # 从环境变量获取账号密码
        self.proxy_username = os.getenv('PROXY_USERNAME')
        self.proxy_password = os.getenv('PROXY_PASSWORD')
        
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """获取可用代理"""
        # 检查当前代理是否需要更换（基于时长）
        if self._should_refresh_proxy():
            return self._fetch_new_proxy()

        # 如果有有效代理，直接返回
        if self.current_proxy:
            return self.current_proxy

        # 否则获取新代理
        return self._fetch_new_proxy()

    def _should_refresh_proxy(self) -> bool:
        """检查是否需要刷新代理（基于时长限制）"""
        if not self.current_proxy or not self.proxy_start_time:
            return True

        # 检查代理是否超过生命周期
        elapsed_time = time.time() - self.proxy_start_time
        if elapsed_time >= PROXY_API_CONFIG['proxy_lifetime']:
            logging.info(f"代理已使用 {elapsed_time:.1f} 秒，超过生命周期 {PROXY_API_CONFIG['proxy_lifetime']} 秒，需要更换")
            return True

        return False

    def _fetch_new_proxy(self) -> Optional[Dict[str, str]]:
        """获取新的代理"""
        try:
            # 获取代理API时不使用代理，直接用本地IP
            response = requests.get(
                PROXY_API_CONFIG['url'],
                params=PROXY_API_CONFIG['params'],
                timeout=10
            )

            # 输出详细的返回信息
            print(f"状态码: {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            try:
                # 尝试以 JSON 格式输出响应内容
                print("响应内容 (JSON):")
                print(response.json())
            except ValueError:
                # 若不是 JSON 格式，则以文本形式输出
                print("响应内容 (文本):")
                print(response.text)

            print(PROXY_API_CONFIG)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200 and data.get('data'):
                    proxy_info = data['data'][0]
                    logging.info(f"获取到新代理: {proxy_info['ip']}:{proxy_info['port']}")

                    self.current_proxy = proxy_info # {ip:xx, port:xx}
                    self.proxy_start_time = time.time()  # 记录代理开始使用时间

                    return self.current_proxy
                else:
                    logging.warning(f"代理API返回错误: {data}")
            else:
                logging.warning(f"代理API请求失败: {response.status_code}")

        except Exception as e:
            logging.error(f"获取代理失败: {e}")

        logging.warning("无法获取可用代理，将使用本地IP")
        self.current_proxy = None
        self.proxy_start_time = None
        return None
        
    def should_rotate_proxy(self) -> bool:
        """判断是否需要轮换代理（基于时长）"""

        # 基于时长的轮换（每5分钟）
        if self._should_refresh_proxy():
            return True

        return False

    def get_proxy_info(self) -> Dict:
        """获取当前代理的详细信息"""
        if not self.current_proxy or not self.proxy_start_time:
            return {
                'proxy_info': None,
                'start_time': None,
                'elapsed_time': 0,
                'remaining_time': 0,
            }

        elapsed_time = time.time() - self.proxy_start_time
        remaining_time = max(0, PROXY_API_CONFIG['proxy_lifetime'] - elapsed_time)

        return {
            'proxy_info': self.current_proxy,      # 直接返回当前代理
            'start_time': self.proxy_start_time,
            'elapsed_time': elapsed_time,
            'remaining_time': remaining_time,
        }

class ProxyPoolManager:
    """代理池管理器"""
    def __init__(self, pool_size: int=10):
        '''
        每个 proxy item 都是一个 map:
        {"ip":"***************", "port":40025, start_time: time.time(), expire_time: time.time() + ttl}
        '''

        self.pool_size = pool_size

        # 每个 item_map {} 包含 ip, port, start_time, expire_time
        self.cur_proxys = []
        self.cur_index = 0

        # 构建请求参数
        self.ttl = 180 - 10 # 代理有效期 (10s 预留)
        self.url = 'http://api.shenlongip.com/ip'
        self.params = {
            'key': os.getenv('PROXY_API_KEY'),
            'protocol': 1,  # 1=HTTP, 2=HTTPS - 统一使用HTTP
            'mr': 1,
            'pattern': 'json',
            'need': 1000,
            'count': 1,
            'sign': os.getenv('PROXY_API_SIGN')
        }

    def _get_new_proxy(self, depth: int = 0) -> Optional[Dict[str, str]]:
        '''从神龙代理获取新的代理'''
        # 1. 获取代理 API 请求
        response = requests.get(
            self.url,
            params=self.params,
            timeout=10
        )

        # 2. 解析代理信息
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 200 and data.get('data'):
                proxy_info = data['data'][0]
                proxy_info['start_time'] = time.time()
                proxy_info['expire_time'] = proxy_info['start_time'] + self.ttl # 180s 后失效
                return proxy_info # {ip:xx, port:xx, start_time: time.time(), expire_time: time.time() + 180}
            
        # 3. 获取失败
        logging.warning(f"获取新 proxy 失败: {response}")
        if depth < 3:
            time.sleep(1)
            return self._get_new_proxy(depth + 1)
        
        return None

    def _refresh_proxys(self):
        '''刷新所有代理（先判断是否需要刷新）'''
        # 1. 当代理池为空时，直接获取最大数量的代理
        if len(self.cur_proxys) == 0:
            self.cur_proxys = [self._get_new_proxy() for _ in range(self.pool_size)]
            self.cur_proxys = [proxy for proxy in self.cur_proxys if proxy]
            return

        # 2. 当代理池不为空时，检查每个代理是否过期（expire_time < time.time()）
        for proxy in self.cur_proxys:
            if proxy['expire_time'] < time.time():
                self.cur_proxys.remove(proxy)
                new_proxy = self._get_new_proxy()
                if new_proxy: # 只添加有效的代理
                    self.cur_proxys.append(new_proxy)

        # 3. 如果代理池中有效数量不足，补充代理
        self.cur_proxys = [proxy for proxy in self.cur_proxys if proxy]
        if len(self.cur_proxys) < self.pool_size:
            needed = self.pool_size - len(self.cur_proxys)
            new_proxys = [self._get_new_proxy() for _ in range(needed)]
            new_proxys = [proxy for proxy in new_proxys if proxy]

            self.cur_proxys.extend(new_proxys)

    def get_proxy(self) -> Optional[Dict[str, str]]:
        """从代理池顺序获取一个 ip"""
        # 1. 刷新代理池
        self._refresh_proxys()
        if len(self.cur_proxys) == 0: return None

        # 2. 获取代理
        self.cur_index = self.cur_index % len(self.cur_proxys)
        proxy = self.cur_proxys[self.cur_index]
        self.cur_index += 1

        return proxy

    def get_proxy_url(self, with_auth: bool = False) -> Optional[str]:
        '''获取代理 url'''
        proxy = self.get_proxy()
        if proxy:
            # 从环境变量获取代理认证信息
            proxy_username = os.getenv('PROXY_USERNAME')
            proxy_password = os.getenv('PROXY_PASSWORD')

            if proxy_username and proxy_password and with_auth:
                # 带认证的代理格式 - 统一使用HTTP协议
                proxy_url = f"http://{proxy_username}:{proxy_password}@{proxy['ip']}:{proxy['port']}"
            else:
                # 不带认证的代理格式 - 统一使用HTTP协议
                proxy_url = f"http://{proxy['ip']}:{proxy['port']}"

            return proxy_url

        return None

class LiteDriver:
    """轻量级驱动管理器 - 使用selenium-wire支持代理认证"""

    def __init__(self):
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        self.driver = None

    def create_driver(self, proxy_url: str = None):
        """创建Chrome驱动 - 使用selenium-wire支持代理认证"""
        # selenium-wire 选项
        seleniumwire_options = {}

        if proxy_url:
            # 解析代理URL获取认证信息
            if '@' in proxy_url:
                # 格式: ***************************:port
                protocol_part, rest = proxy_url.split('://', 1)
                auth_part, server_part = rest.split('@', 1)
                username, password = auth_part.split(':', 1)

                seleniumwire_options['proxy'] = {
                    'http': f'http://{server_part}',
                    'https': f'http://{server_part}',  # HTTPS流量也通过HTTP代理
                    'username': username,
                    'password': password
                }
            else:
                # 格式: http://ip:port (无认证)
                seleniumwire_options['proxy'] = {
                    'http': proxy_url,
                    'https': proxy_url.replace('http://', 'http://'),  # 统一使用HTTP
                }

        # Chrome选项
        chrome_options = uc.ChromeOptions()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument(f'--user-agent={self.user_agent}')

        # 使用selenium-wire创建驱动
        self.driver = wire_webdriver.Chrome(
            options=chrome_options,
            seleniumwire_options=seleniumwire_options
        )

        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.set_window_size(1920, 1080)
        return self.driver

    def safe_get(self, url: str, max_retries: int = 3, proxy_url: str = None) -> str:
        """安全地访问URL - 通用网页访问功能"""

        self.driver = self.create_driver(proxy_url)

        # 设置页面加载超时时间
        self.driver.set_page_load_timeout(10)

        for attempt in range(max_retries):
            try:
                logging.info(f"访问URL: {url} (尝试 {attempt + 1}/{max_retries})")
                self.driver.get(url)
                time.sleep(8)

                result = self.driver.page_source

                self.driver.quit()
                return result

            except Exception as e:
                logging.warning(f"访问URL失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(1, 3))

        self.driver.quit()
        return None

    def quit(self):
        """关闭驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("浏览器驱动已关闭")
            except Exception as e:
                logging.warning(f"关闭浏览器驱动时出错: {e}")
            finally:
                self.driver = None

class WireDriverManager:
    """基于selenium-wire的Chrome驱动管理器 - 专门处理代理认证"""

    def __init__(self, use_proxy=False):
        self.proxy_manager = ProxyManager()
        self.proxy_username = os.getenv('PROXY_USERNAME')
        self.proxy_password = os.getenv('PROXY_PASSWORD')

        self.use_proxy = use_proxy  # 是否使用代理
        self.driver = None
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

    def create_driver(self):
        """创建Chrome驱动 - 使用selenium-wire支持代理认证"""
        # selenium-wire 选项
        seleniumwire_options = {}

        if self.use_proxy:
            proxy = self.proxy_manager.get_proxy()
            if proxy:
                proxy_server = f"{proxy['ip']}:{proxy['port']}"

                if self.proxy_username and self.proxy_password:
                    # 带认证的代理配置
                    seleniumwire_options['proxy'] = {
                        'http': f'http://{proxy_server}',
                        'https': f'http://{proxy_server}',  # HTTPS流量也通过HTTP代理
                        'username': self.proxy_username,
                        'password': self.proxy_password
                    }
                    logging.info(f"使用带认证的HTTP代理: {proxy_server}")
                else:
                    # 无认证的代理配置
                    seleniumwire_options['proxy'] = {
                        'http': f'http://{proxy_server}',
                        'https': f'http://{proxy_server}',
                    }
                    logging.info(f"使用HTTP代理: {proxy_server}")

        # Chrome选项
        chrome_options = uc.ChromeOptions()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument(f'--user-agent={self.user_agent}')

        # 使用selenium-wire创建驱动
        self.driver = wire_webdriver.Chrome(
            options=chrome_options,
            seleniumwire_options=seleniumwire_options
        )

        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.set_window_size(1920, 1080)
        return self.driver

    def safe_get(self, url: str, max_retries: int = 3) -> bool:
        """安全地访问URL"""
        if not self.driver:
            raise RuntimeError("驱动未初始化，请先调用create_driver()")

        for attempt in range(max_retries):
            try:
                logging.info(f"访问URL: {url} (尝试 {attempt + 1}/{max_retries})")
                self.driver.get(url)
                WebDriverWait(self.driver, 10).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
                time.sleep(2)  # 等待页面完全加载
                return True
            except Exception as e:
                logging.warning(f"访问URL失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(2, 5))
        return False

    def extract_text_safe(self, selector: str, multiple: bool = False):
        """安全地提取文本内容"""
        if not self.driver:
            raise RuntimeError("驱动未初始化，请先调用create_driver()")

        try:
            if multiple:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                return [elem.text.strip() for elem in elements if elem.text.strip()]
            else:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                return element.text.strip() if element.text.strip() else None
        except:
            return None if not multiple else []

    def extract_attribute_safe(self, selector: str, attribute: str) -> Optional[str]:
        """安全地提取元素属性"""
        if not self.driver:
            raise RuntimeError("驱动未初始化，请先调用create_driver()")

        try:
            element = self.driver.find_element(By.CSS_SELECTOR, selector)
            return element.get_attribute(attribute)
        except:
            return None

    def quit(self):
        """关闭驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("浏览器驱动已关闭")
            except Exception as e:
                logging.warning(f"关闭浏览器驱动时出错: {e}")
            finally:
                self.driver = None

class DriverManager:
    """Chrome驱动管理器 - 通用驱动管理功能"""

    def __init__(self, use_proxy=False):
        self.proxy_manager = ProxyManager()
        self.proxy_username = os.getenv('PROXY_USERNAME')
        self.proxy_password = os.getenv('PROXY_PASSWORD')

        self.use_proxy = use_proxy  # 是否使用代理
        self.driver = None
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

    def create_driver(self) -> uc.Chrome:
        """创建Chrome驱动"""
        options = uc.ChromeOptions()
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-images')
        options.add_argument(f'--user-agent={self.user_agent}')


        # 配置代理 + 账号密码验证（修复协议重复问题）
        if self.use_proxy:
            proxy = self.proxy_manager.get_proxy()
            proxy_part = f"{proxy['ip']}:{proxy['port']}"
            # 修复：移除重复的协议前缀
            if self.proxy_username and self.proxy_password:
                proxy_url = f"http://{self.proxy_username}:{self.proxy_password}@{proxy_part}"
            else:
                proxy_url = f"http://{proxy_part}"

            options.add_argument(f'--proxy-server={proxy_url}')
            logging.info(f"使用代理: {proxy_part} (协议: HTTP)")

        self.driver = uc.Chrome(options=options, version_main=None, driver_executable_path=DRIVER_PATH)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.set_window_size(1920, 1080)
        return self.driver

    def check_and_handle_verification(self, max_wait_time: int = 30) -> bool:
        """检测并处理验证页面"""
        if not self.driver:
            raise RuntimeError("驱动未初始化，请先调用create_driver()")

        try:
            # 获取页面源码和标题
            page_source = self.driver.page_source.lower()
            page_title = self.driver.title.lower()

            # 检测验证相关关键词
            verification_keywords = [
                '验证', '检测', '人机验证', '安全验证', '确认您是真人', '真人', 'security check',
                'please verify', 'are you human', 'checking your browser'
            ]

            # 检查是否包含验证关键词
            has_verification = any(keyword in page_source or keyword in page_title
                                 for keyword in verification_keywords)

            if has_verification:
                logging.warning("🤖 检测到验证页面，正在处理...")

                # 输出关键词附近的 100 个字符
                for keyword in verification_keywords:
                    if keyword in page_source:
                        index = page_source.index(keyword)
                        print(f"关键词 '{keyword}' 附近的内容:")
                        print(page_source[max(0, index-50):index+150])

                time.sleep(10)

                # # 尝试查找并点击验证按钮
                # verification_selectors = [
                #     'input[type="checkbox"]',  # 复选框
                #     'button[type="submit"]',   # 提交按钮
                #     '.verify-button',          # 验证按钮
                #     '#verify-button',
                #     'button:contains("验证")',
                #     'button:contains("Verify")',
                #     'input[value*="verify"]',
                #     '[data-callback]',         # reCAPTCHA
                #     '.g-recaptcha',           # Google reCAPTCHA
                #     '.cf-browser-verification' # Cloudflare
                # ]

                # # 尝试点击验证元素
                # for selector in verification_selectors:
                #     try:
                #         if selector.startswith('button:contains'):
                #             # 对于包含文本的按钮，使用XPath
                #             text = selector.split('"')[1]
                #             xpath = f"//button[contains(text(), '{text}')]"
                #             element = self.driver.find_element(By.XPATH, xpath)
                #         else:
                #             element = self.driver.find_element(By.CSS_SELECTOR, selector)

                #         if element.is_displayed() and element.is_enabled():
                #             logging.info(f"找到验证元素: {selector}")
                #             element.click()
                #             logging.info("已点击验证元素")
                #             break
                #     except:
                #         continue

                # # 等待验证完成
                # logging.info(f"等待验证完成，最多等待 {max_wait_time} 秒...")
                # start_time = time.time()

                # while time.time() - start_time < max_wait_time:
                #     time.sleep(2)

                #     # 重新检查页面内容
                #     current_source = self.driver.page_source.lower()
                #     current_title = self.driver.title.lower()

                #     # 如果不再包含验证关键词，说明验证通过
                #     still_has_verification = any(keyword in current_source or keyword in current_title
                #                                for keyword in verification_keywords)

                #     if not still_has_verification:
                #         logging.info("✅ 验证已通过")
                #         return True

                # logging.warning(f"⏰ 验证等待超时 ({max_wait_time}秒)")
                # return False

            return True  # 没有验证页面

        except Exception as e:
            logging.error(f"处理验证页面时出错: {e}")
            return False

    def safe_get(self, url: str, max_retries: int = 3) -> bool:
        """安全地访问URL - 通用网页访问功能"""
        if not self.driver:
            raise RuntimeError("驱动未初始化，请先调用create_driver()")

        for attempt in range(max_retries):
            try:
                logging.info(f"访问URL: {url} (尝试 {attempt + 1}/{max_retries})")
                self.driver.get(url)
                WebDriverWait(self.driver, 10).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )

                # 检查并处理验证页面
                if not self.check_and_handle_verification():
                    logging.warning("验证处理失败，但继续尝试访问")

                # time.sleep(1)
                return True
            except Exception as e:
                logging.warning(f"访问URL失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 10))
        return False

    def extract_text_safe(self, selector: str, multiple: bool = False):
        """安全地提取文本内容 - 通用文本提取功能"""
        if not self.driver:
            raise RuntimeError("驱动未初始化，请先调用create_driver()")

        try:
            if multiple:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                return [elem.text.strip() for elem in elements if elem.text.strip()]
            else:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                return element.text.strip() if element.text.strip() else None
        except:
            return None if not multiple else []

    def extract_attribute_safe(self, selector: str, attribute: str) -> Optional[str]:
        """安全地提取元素属性 - 通用属性提取功能"""
        if not self.driver:
            raise RuntimeError("驱动未初始化，请先调用create_driver()")

        try:
            element = self.driver.find_element(By.CSS_SELECTOR, selector)
            return element.get_attribute(attribute)
        except:
            return None

    def quit(self):
        """关闭驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("浏览器驱动已关闭")
            except Exception as e:
                logging.warning(f"关闭浏览器驱动时出错: {e}")
            finally:
                self.driver = None

def test_proxy():
    proxy_manager = ProxyPoolManager()
    out = proxy_manager.get_proxy()
    print(out)
    out = proxy_manager.get_proxy()
    print(out)
    out = proxy_manager.get_proxy()
    print(out)
    # 测试代码

def test_driver():
    """测试驱动管理器和网站内容提取"""
    print("=" * 60)
    print("🌐 测试网站内容提取")
    print("=" * 60)

    driver_manager = DriverManager()

    try:
        print("1. 创建浏览器驱动...")
        driver = driver_manager.create_driver()
        print("✅ 驱动创建成功")

        # 测试网站列表
        test_urls = [
            "https://www.baidu.com/index.htm",
            "https://dl.acm.org/journal/tkdd",
            "http://httpbin.org/html"
        ]

        for i, url in enumerate(test_urls, 1):
            print(f"\n{i}. 测试网站: {url}")

            # 访问网站
            if driver_manager.safe_get(url):
                print("   ✅ 网站访问成功")

                # 获取页面标题
                try:
                    page_title = driver.title
                    print(f"   页面标题: {page_title}")
                except Exception as e:
                    print(f"   ❌ 获取页面标题失败: {e}")

                # 获取页面源码（前300字符）
                try:
                    page_source = driver.page_source
                    print(f"   页面源码长度: {len(page_source)} 字符")
                    print(f"   页面源码预览:")
                    print("   " + "-" * 50)
                    print("   " + page_source[:300].replace('\n', '\n   ') + "...")
                    print("   " + "-" * 50)
                except Exception as e:
                    print(f"   ❌ 获取页面源码失败: {e}")

                # 尝试提取一些常见元素
                selectors_to_test = ['h1', 'title', '.page-title', 'h2']
                for selector in selectors_to_test:
                    try:
                        element_text = driver_manager.extract_text_safe(selector)
                        if element_text:
                            print(f"   找到元素 {selector}: {element_text[:80]}...")
                            break
                    except:
                        continue

            else:
                print("   ❌ 网站访问失败")

            print("   " + "=" * 40)

        print("\n✅ 网站内容提取测试完成")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭驱动
        driver_manager.quit()
        print("🔒 浏览器驱动已关闭")

def test_lite_driver_with_proxy():
    """测试轻量级驱动管理器"""
    proxy_fetcher = ProxyPoolManager(pool_size=1)
    proxy_url = proxy_fetcher.get_proxy_url(with_auth=True)

    lite_driver = LiteDriver()
    test_urls = [
        "https://www.baidu.com/",
    ]

    for url in test_urls:
        result = lite_driver.safe_get(url, proxy_url=proxy_url) # 加 url 会导致重定向失败
        print('摘要', result)

def test_wire_driver_with_proxy():
    """测试基于selenium-wire的驱动管理器"""
    print("=" * 60)
    print("🌐 测试selenium-wire代理驱动")
    print("=" * 60)

    # 创建带代理的驱动管理器
    wire_driver = WireDriverManager(use_proxy=True)

    try:
        print("1. 创建selenium-wire浏览器驱动...")
        driver = wire_driver.create_driver()
        print("✅ 驱动创建成功")

        # 测试网站列表
        test_urls = [
            "https://www.baidu.com/",
            "https://dl.acm.org/journal/tkdd",
            "http://httpbin.org/ip"  # 显示当前IP地址
        ]

        for i, url in enumerate(test_urls, 1):
            print(f"\n{i}. 测试网站: {url}")

            # 访问网站
            if wire_driver.safe_get(url):
                print("   ✅ 网站访问成功")

                # 获取页面标题
                try:
                    page_title = driver.title
                    print(f"   页面标题: {page_title}")
                except Exception as e:
                    print(f"   ❌ 获取页面标题失败: {e}")

                # 获取页面源码（前300字符）
                try:
                    page_source = driver.page_source
                    print(f"   页面源码长度: {len(page_source)} 字符")
                    if "httpbin.org/ip" in url:
                        # 对于IP检测网站，显示完整内容
                        print(f"   IP检测结果: {page_source}")
                    else:
                        print(f"   页面源码预览:")
                        print("   " + "-" * 50)
                        print("   " + page_source[:300].replace('\n', '\n   ') + "...")
                        print("   " + "-" * 50)
                except Exception as e:
                    print(f"   ❌ 获取页面源码失败: {e}")

            else:
                print("   ❌ 网站访问失败")

            print("   " + "=" * 40)

        print("\n✅ selenium-wire代理测试完成")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭驱动
        wire_driver.quit()
        print("🔒 浏览器驱动已关闭")

def test_acm_with_wire_driver():
    """测试ACM网站访问 - 使用selenium-wire"""
    print("=" * 60)
    print("🌐 测试ACM网站访问 - selenium-wire")
    print("=" * 60)

    # 创建带代理的驱动管理器
    wire_driver = WireDriverManager(use_proxy=True)

    try:
        print("1. 创建selenium-wire浏览器驱动...")
        driver = wire_driver.create_driver()
        print("✅ 驱动创建成功")

        # ACM测试URL
        acm_url = "https://dl.acm.org/doi/abs/10.1145/3679018"
        print(f"\n2. 测试ACM论文页面: {acm_url}")

        # 访问ACM网站
        if wire_driver.safe_get(acm_url):
            print("   ✅ ACM网站访问成功")

            # 获取页面标题
            try:
                page_title = driver.title
                print(f"   页面标题: {page_title}")
            except Exception as e:
                print(f"   ❌ 获取页面标题失败: {e}")

            # 尝试提取论文标题
            try:
                paper_title = wire_driver.extract_text_safe('h1.citation__title')
                if paper_title:
                    print(f"   论文标题: {paper_title}")
                else:
                    print("   ⚠️ 未找到论文标题")
            except Exception as e:
                print(f"   ❌ 提取论文标题失败: {e}")

            # 尝试提取摘要
            try:
                abstract = wire_driver.extract_text_safe('div.abstractSection')
                if abstract:
                    print(f"   摘要: {abstract[:200]}...")
                else:
                    print("   ⚠️ 未找到摘要")
            except Exception as e:
                print(f"   ❌ 提取摘要失败: {e}")

            # 检查页面源码中是否包含关键内容
            try:
                page_source = driver.page_source
                if "citation__title" in page_source:
                    print("   ✅ 页面包含论文标题元素")
                else:
                    print("   ⚠️ 页面不包含论文标题元素")

                if "abstractSection" in page_source:
                    print("   ✅ 页面包含摘要元素")
                else:
                    print("   ⚠️ 页面不包含摘要元素")

                print(f"   页面源码长度: {len(page_source)} 字符")
            except Exception as e:
                print(f"   ❌ 检查页面源码失败: {e}")

        else:
            print("   ❌ ACM网站访问失败")

        print("\n✅ ACM网站测试完成")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭驱动
        wire_driver.quit()
        print("🔒 浏览器驱动已关闭")

if __name__ == "__main__":
    # test_lite_driver_with_proxy()
    # test_wire_driver_with_proxy()
    test_acm_with_wire_driver()
    pass