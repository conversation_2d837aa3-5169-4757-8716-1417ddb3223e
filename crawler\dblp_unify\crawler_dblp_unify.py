#!/usr/bin/env python3
"""
DBLP API 论文获取模块
通过DBLP API获取指定会议或期刊的论文信息

新增异步处理功能：
1. AsyncAbstractFetcher: 异步摘要获取器
2. 支持高并发异步请求
3. 更高效的I/O处理
"""

import os
import re
import sys
import json
import requests
import logging
import time
import asyncio
import aiohttp
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
from tqdm import tqdm
from fake_useragent import UserAgent
from bs4 import BeautifulSoup


# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_DIR)

# 导入CCF A类会议规则
from utils.utils import ProxyPoolManager
from crawler.dblp_unify.special_rules import get_special_rules
from crawler.dblp_unify.venue import (
    get_venue_name,
    get_all_venue_by_rule,
)


class DBLPPaperFetcher:
    """DBLP论文获取器"""

    def __init__(self, data_dir: str):
        self.base_url = 'https://dblp.org/search/publ/api'
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DBLP-Paper-Fetcher/1.0'
        })

        # 创建保存目录
        self.data_dir = data_dir
        if not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)

    def _send_request(self, query: str, max_hits: int = 1000, start_from: int = 0) -> Optional[Dict]:
        """
        发送DBLP API请求，带有重试机制

        Args:
            query: 查询字符串
            max_hits: 最大返回结果数
            start_from: 起始位置

        Returns:
            API响应的JSON数据
        """
        params = {
            'q': query,
            'format': 'json',
            'h': min(max_hits, 1000),  # DBLP API限制最大1000
            'f': start_from,
            'c': 0  # 不需要自动补全
        }

        # 重试配置：延迟时间（秒）
        retry_delays = [10, 30, 60, 120, 300]  # 10s, 30s, 1min, 2min, 5min
        max_retries = len(retry_delays)

        for attempt in range(max_retries + 1):  # +1 是因为第一次不算重试
            try:
                response = self.session.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                if attempt < max_retries:
                    delay = retry_delays[attempt]
                    logging.warning(f"⚠️ DBLP API请求失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                    logging.info(f"🔄 等待 {delay} 秒后重试...")
                    time.sleep(delay)
                else:
                    logging.error(f"❌ DBLP API请求最终失败，已重试 {max_retries} 次: {e}")
                    return None

    def _extract_paper_info(self, hit: Dict) -> Dict:
        """
        从DBLP API响应中提取论文信息

        Args:
            hit: DBLP API返回的单篇论文数据

        Returns:
            标准化的论文信息字典
        """
        info = hit.get('info', {})

        # 添加DBLP特有的字段
        paper_info = info.copy()
        paper_info['key'] = hit.get('@id', '')  # DBLP key
        paper_info['dblp_url'] = f"https://dblp.org/rec/{hit.get('@id', '')}" if hit.get('@id') else ''

        # 确保ee字段是列表格式
        if 'ee' in paper_info and isinstance(paper_info['ee'], str):
            paper_info['ee'] = [paper_info['ee']]

        return paper_info

    def _get_all_papers_by_page(self, query: str) -> List[Dict]:
        """
        分页获取所有论文数据

        Args:
            query: 查询字符串

        Returns:
            所有论文信息列表
        """
        all_papers = []
        start_from = 0
        page_size = 1000

        while True:
            result = self._send_request(query, max_hits=page_size, start_from=start_from)

            if not result or 'result' not in result:
                break

            hits_data = result['result'].get('hits', {})
            total = int(hits_data.get('@total', 0))
            hits = hits_data.get('hit', [])
            
            if not hits:
                break

            # 处理当前页的论文
            for hit in hits:
                paper_info = self._extract_paper_info(hit)
                all_papers.append(paper_info)

            # 检查是否还有更多数据
            if len(all_papers) >= total or len(hits) < page_size:
                break

            start_from += page_size
            time.sleep(0.5)  # 避免请求过快

        return all_papers

    def _replace_special_characters(self, text: any) -> str:
        if not text: return ''
        text = str(text)
        # 使用正则表达式替换所有非字母字符为空格
        text = re.sub(r'[^a-zA-Z\s]', ' ', text)
        # 将多个连续空格替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        # 去除首尾空格
        text = text.strip()
        # 转换为小写
        text = text.lower()

        return text

    def fetch_papers(self, venue_name: str, year: int) -> List[Dict]:
        """
        获取指定会议/期刊和年份的所有论文

        Args:
            venue_name: 会议或期刊名称 (如 'tocs', 'sigmod', 'vldb')
            year: 年份

        Returns:
            论文信息列表
        """
        # 1. 获取正确的venue名称
        dblp_venue_name = get_venue_name(venue_name, year)
        query = f"venue:{dblp_venue_name} year:{year}"

        # 2. 获取论文
        papers = self._get_all_papers_by_page(query)

        # 3. 过滤论文，只保留精确匹配的venue（DBLP API查询和返回结果的venue名称格式可能不一致）
        filtered_papers = []

        # 确定要匹配的venue名称列表
        target_venues = [self._replace_special_characters(dblp_venue_name)] # 原始 venue 名称
        special_rules = get_special_rules(venue_name) # 特殊规则（有些名称多）
        if 'filter_venues' in special_rules:
            target_venues.extend([self._replace_special_characters(venue) for venue in special_rules['filter_venues']])

        for paper in papers:
            paper_venue = paper.get('venue', '')
            # 将 paper_venue 转换为一个字符串列表
            if isinstance(paper_venue, list):
                paper_venue = [self._replace_special_characters(venue) for venue in paper_venue]
            else:
                paper_venue = [self._replace_special_characters(paper_venue)]

            # 检查是否匹配任何目标venue名称（查看是否有交集）
            if set(paper_venue).intersection(target_venues):
                filtered_papers.append(paper)

        # 4. 过滤前信息统计
        type_counts_before = {}
        venue_counts_before = {}
        for p in papers:
            # 4.1 类型分布
            paper_type = p.get('type', 'unknown')
            type_counts_before[paper_type] = type_counts_before.get(paper_type, 0) + 1

            # 4.2 来源分布
            paper_venue = p.get('venue', 'unknown')
            if isinstance(paper_venue, list):
                paper_venue = ', '.join(paper_venue) if paper_venue else 'unknown'
            elif not isinstance(paper_venue, str):
                paper_venue = str(paper_venue) if paper_venue else 'unknown'
            venue_counts_before[paper_venue] = venue_counts_before.get(paper_venue, 0) + 1

        logging.info(f"✅ {dblp_venue_name}'{year}: 过滤前 {len(papers)} -> 过滤后 {len(filtered_papers)}")

        # 5. 过滤后信息统计
        type_counts = {}
        venue_counts = {}
        for p in filtered_papers:
            # 5.1 类型分布
            paper_type = p.get('type', 'unknown')
            type_counts[paper_type] = type_counts.get(paper_type, 0) + 1

            # 5.2 来源分布
            paper_venue = p.get('venue', 'unknown')
            if isinstance(paper_venue, list):
                paper_venue = ', '.join(paper_venue) if paper_venue else 'unknown'
            elif not isinstance(paper_venue, str):
                paper_venue = str(paper_venue) if paper_venue else 'unknown'
            venue_counts[paper_venue] = venue_counts.get(paper_venue, 0) + 1

        return filtered_papers, type_counts, venue_counts, type_counts_before, venue_counts_before

    def save_papers_to_json(self, papers: List[Dict], venue_name: str, year: int,
                           type_counts: Dict, venue_counts: Dict,
                           type_counts_before: Dict, venue_counts_before: Dict,
                           total_papers_before: int) -> str:
        """
        保存论文数据到JSON文件
        Args:
            papers: 过滤后的论文数据列表
            venue_name: 会议/期刊名称
            year: 年份
            type_counts: 过滤后论文类型分布统计
            venue_counts: 过滤后论文来源分布统计
            type_counts_before: 过滤前论文类型分布统计
            venue_counts_before: 过滤前论文来源分布统计
            total_papers_before: 过滤前论文总数
        Returns:
            保存的文件路径
        """
        # 1. 清理文件名中的特殊字符
        clean_venue_name = venue_name.replace(' ', '_').replace('.', '').replace('/', '_')
        filename = f"{clean_venue_name}_{year}.json"
        filepath = os.path.join(self.data_dir, filename)

        # 2. 添加元数据和统计信息
        data_to_save = {
            'metadata': {
                'venue_name': venue_name,
                'year': year,
                'total_papers': len(papers),
                'fetch_time': datetime.now().isoformat(),
                'source': 'DBLP API',
                'type_distribution': type_counts,
                'venue_distribution': venue_counts
            },
            'metadata_before_filtered': {
                'total_papers': total_papers_before,
                'type_distribution': type_counts_before,
                'venue_distribution': venue_counts_before
            },
            'papers': papers
        }

        # 3. 保存到JSON文件
        if total_papers_before == 0:
            return None
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)

        return filepath

    def get_papers_by_venue_and_year(self, venue_name: str, year: int) -> str:
        """
        根据会议/期刊名称和年份获取论文，并保存为JSON格式

        Args:
            venue_name: 会议或期刊名称
            year: 年份

        Returns:
            保存的文件路径
        """
        papers, type_counts, venue_counts, type_counts_before, venue_counts_before = self.fetch_papers(venue_name, year)
        total_papers_before = sum(type_counts_before.values())
        return self.save_papers_to_json(papers, venue_name, year, type_counts, venue_counts,
                                       type_counts_before, venue_counts_before, total_papers_before)

class DBLPPaperInfo:
    """用于对论文数据进行统计和信息的展示"""
    def __init__(self):
        pass

    def info_by_dir(self, dir_path: str):
        """
        从指定目录读取所有JSON文件，展示论文信息
        Args:
            dir_path: 包含JSON文件的目录路径
        """
        from prettytable import PrettyTable
        from pathlib import Path

        if not os.path.exists(dir_path):
            print(f"❌ 目录不存在: {dir_path}")
            return

        # 获取所有JSON文件
        json_files = list(Path(dir_path).glob("*.json"))
        if not json_files:
            print(f"❌ 目录中没有找到JSON文件: {dir_path}")
            return

        print(f"📁 正在分析目录: {dir_path}")
        print(f"📄 找到 {len(json_files)} 个JSON文件")
        print("=" * 80)

        # 收集数据：会议 -> 年份 -> 论文数
        venue_year_data = {}
        all_years = set()

        for json_file in sorted(json_files):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                metadata = data.get('metadata', {})
                venue_name = metadata.get('venue_name', 'N/A')
                year = metadata.get('year', 'N/A')
                total_papers = metadata.get('total_papers', 0)

                if venue_name not in venue_year_data:
                    venue_year_data[venue_name] = {}

                venue_year_data[venue_name][year] = total_papers
                all_years.add(year)

            except Exception as e:
                print(f"⚠️ 读取文件失败: {json_file.name} - {e}")

        # 排序年份
        sorted_years = sorted([y for y in all_years if y != 'N/A'])
        if 'N/A' in all_years:
            sorted_years.append('N/A')

        # 创建表格
        table = PrettyTable()
        table.field_names = ['会议'] + [str(year) for year in sorted_years]

        # 添加数据行
        for venue in sorted(venue_year_data.keys()):
            row = [venue]
            for year in sorted_years:
                count = venue_year_data[venue].get(year, 0)
                row.append(count if count > 0 else '-')
            table.add_row(row)

        # 设置表格样式
        table.align = 'r'  # 右对齐
        table.align['会议'] = 'l'  # 会议名左对齐

        print("\n📊 会议-年份论文数量统计表:")
        print(table)

        # 简单统计
        total_papers = sum(sum(years.values()) for years in venue_year_data.values())
        total_venues = len(venue_year_data)
        print(f"\n📈 汇总: {total_venues} 个会议, 共 {total_papers:,} 篇论文")

class AsyncAbstractFetcher:
    """
    异步论文摘要获取器
    
    使用异步I/O处理，支持高并发请求，显著提高处理效率
    """
    
    def __init__(self, max_concurrent: int = 100):
        """
        初始化异步摘要获取器
        
        Args:
            max_concurrent: 最大并发请求数
        """
        self.crossref_base_url = "https://api.crossref.org/works/"
        self.openalex_base_url = "https://api.openalex.org/works/"
        self.semantic_scholar_base_url = "https://api.semanticscholar.org/v1/paper/"
        self.ua = UserAgent()

        # 线程池
        self.proxy_pool = ProxyPoolManager(pool_size=10)

        # 异步配置
        self.max_concurrent = max_concurrent
        self.semaphore = None
        self.session = None
        
        # 统计信息
        self.stats_map = {}
        self.stats_keys = [
            'total_papers',
            'papers_with_abstract',
            'papers_without_doi',
            'papers_abstract_fetched',
            'papers_abstract_failed'
        ]

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers={
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            },
            timeout=aiohttp.ClientTimeout(total=3)
        )
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _clean_abstract(self, abstract: str) -> str:
        """
        清理摘要文本，移除HTML标签和多余空白字符
        
        Args:
            abstract: 原始摘要文本
            
        Returns:
            清理后的摘要文本
        """
        import re
        # 移除HTML标签
        abstract = re.sub(r'<[^>]+>', '', abstract)
        # 移除多余的空白字符
        abstract = re.sub(r'\s+', ' ', abstract).strip()
        return abstract
    
    async def _request_with_retry_async(self, url: str, doi: str, api_name: str = "API") -> Optional[dict]:
        """
        异步带重试机制的API请求
        
        Args:
            url: API请求URL
            doi: 论文DOI（用于日志）
            api_name: API名称（用于日志）
            
        Returns:
            API响应的JSON数据，如果失败返回None
        """
        # 重试配置：延迟时间（秒）
        retry_delays = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 3, 10, 30]  # 前 6 次是排除代理问题，后 3 次是排除API问题
        max_retries = len(retry_delays)
        
        async with self.semaphore:  # 限制并发数
            for attempt in range(max_retries + 1):
                try:
                    async with self.session.get(url, proxy=self.proxy_pool.get_proxy_url()) as response:
                        if response.status == 404:
                            return None
                        response.raise_for_status()
                        return await response.json()
                        
                except Exception as e:
                    if attempt < max_retries:
                        delay = retry_delays[attempt]
                        # logging.warning(f"{api_name}获取摘要时发生错误 {doi} (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        await asyncio.sleep(delay)
                    else:
                        logging.error(f"{api_name}获取摘要最终失败，已重试 {max_retries} 次: {doi} - {e}")
                        return None
        
        return None
    
    async def _request_html_with_retry_async(self, url: str, source_name: str = "网页") -> Optional[str]:
        """
        异步带重试机制的HTML页面请求
        
        Args:
            url: 网页URL
            source_name: 来源名称（用于日志）
            
        Returns:
            网页的HTML内容，如果失败返回None
        """
        # 重试配置：延迟时间（秒）
        retry_delays = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 3, 10, 30]
        max_retries = len(retry_delays)
        
        async with self.semaphore:  # 限制并发数
            for attempt in range(max_retries + 1):
                try:
                    async with self.session.get(url, proxy=self.proxy_pool.get_proxy_url()) as response:
                        if response.status == 404:
                            return None
                        response.raise_for_status()
                        return await response.text()
                        
                except Exception as e:
                    if attempt < max_retries:
                        delay = retry_delays[attempt]
                        if delay > 1:
                            print(f"⚠️ {source_name} 获取页面失败，等待 {delay} 秒后重试: {url}, 错误信息: {e}")
                        await asyncio.sleep(delay)
                    else:
                        logging.error(f"{source_name}获取页面最终失败，已重试 {max_retries} 次: {url} - {e}")
                        return None
        
        return None

    # ========================== 多方式通过 url 获取摘要 ==========================

    async def fetch_abstract_from_openreview(self, url: str) -> Optional[str]:
        """
        异步从OpenReview API获取论文摘要
        """
        pass

    async def fetch_abstract_from_acm(self, url: str) -> Optional[str]:
        """
        异步从ACM 页面获取论文摘要
        """
        html = await self._request_html_with_retry_async(url, "ACM")
        if html is None:
            return None
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html, 'html.parser')
        
        # 查找摘要section - ACM页面通常有id为"abstract"的section
        abstract_section = soup.find('section', id='abstract')
        if abstract_section:
            # 查找所有段落元素
            paragraphs = abstract_section.find_all('div', role='paragraph')
            if paragraphs:
                # 将所有段落文本合并
                abstract_text = ' '.join([p.get_text(strip=True) for p in paragraphs])
                return abstract_text

        return None

    async def fetch_abstract_from_acl(self, url: str) -> Optional[str]:
        """
        异步从 ACL Anthology 页面获取论文摘要
        """
        html = await self._request_html_with_retry_async(url, "ACL Anthology")
        if html is None:
            return None
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html, 'html.parser')
        
        # 查找摘要元素 - 通常包含在class为"acl-abstract"的div中
        abstract_div = soup.find('div', class_='acl-abstract')
        if abstract_div:
            # 查找摘要内容 - 通常在span标签中
            abstract_span = abstract_div.find('span')
            if abstract_span:
                # 获取摘要文本并清理
                abstract_text = abstract_span.get_text(strip=True)
                return abstract_text
        
        return None

    async def fetch_abstract_from_arxiv(self, url: str) -> Optional[str]:
        """
        异步从 arxiv 页面获取论文摘要
        """
        pass

    async def fetch_abstract_from_nips(self, url: str) -> Optional[str]:
        """
        异步从 NIPS proceedings 页面获取论文摘要
        """
        pass

    async def fetch_abstract_from_usenix(self, url: str) -> Optional[str]:
        """
        异步从 usenix 页面获取论文摘要 (FAST, NSDI, OSDI)
        """
        pass

    async def fetch_abstract_from_ndss(self, url: str) -> Optional[str]:
        """
        异步从 ndss 页面获取论文摘要 (ndss)
        """
        pass

    async def fetch_abstract_from_openaccess(self, url: str) -> Optional[str]:
        """
        异步从 openaccess 页面获取论文摘要 (cvpr)
        """
        pass

    async def fetch_abstract_from_ijcai(self, url: str) -> Optional[str]:
        """
        异步从 ijcai 页面获取论文摘要 (ijcai)
        """
        pass
    
    async def fetch_abstract_by_url_async(self, url: str) -> Optional[str]:
        """
        异步使用多个API源获取摘要，具有回退机制

        Args:
            url: 论文URL
            
        Returns:
            论文摘要文本，如果所有API都获取失败返回None
        """

        if 'aclanthology' in url:
            return await self.fetch_abstract_from_acl(url)
        elif 'dl.acm.org' in url:
            return await self.fetch_abstract_from_acm(url)
        elif 'openaccess' in url:
            return await self.fetch_abstract_from_openaccess(url)
        elif 'ijcai' in url:
            return await self.fetch_abstract_from_ijcai(url)
        elif 'usenix' in url:
            return await self.fetch_abstract_from_usenix(url)
        elif 'ndss' in url:
            return await self.fetch_abstract_from_ndss(url)
        elif 'nips' in url:
            return await self.fetch_abstract_from_nips(url)
        elif 'arxiv' in url:
            return await self.fetch_abstract_from_arxiv(url)
        else:
            return None

    # ========================== 多方式通过 doi 获取摘要 ==========================

    async def fetch_abstract_from_crossref(self, doi: str) -> Optional[str]:
        """
        异步从CrossRef API获取论文摘要
        
        Args:
            doi: 论文DOI
            
        Returns:
            论文摘要文本，如果获取失败返回None
        """
        url = f"{self.crossref_base_url}{doi}"
        data = await self._request_with_retry_async(url, doi, "CrossRef")
        
        if data is None:
            return None
        
        message = data.get('message', {})
        abstract = message.get('abstract', '')
        
        if abstract:
            return self._clean_abstract(abstract)
        
        return None
    
    async def fetch_abstract_from_openalex(self, doi: str) -> Optional[str]:
        """
        异步从OpenAlex API获取论文摘要
        
        Args:
            doi: 论文DOI
            
        Returns:
            论文摘要文本，如果获取失败返回None
        """
        url = f"{self.openalex_base_url}doi:{doi}"
        data = await self._request_with_retry_async(url, doi, "OpenAlex")
        
        if data is None:
            return None
        
        # OpenAlex返回的摘要可能在abstract_inverted_index字段中
        abstract_inverted_index = data.get('abstract_inverted_index', {})
        
        if abstract_inverted_index:
            # 将倒排索引转换为完整文本
            word_positions = []
            for word, positions in abstract_inverted_index.items():
                for pos in positions:
                    word_positions.append((pos, word))
            
            # 按位置排序
            word_positions.sort(key=lambda x: x[0])
            
            # 重建摘要文本
            abstract = ' '.join([word for _, word in word_positions])
            
            if abstract:
                return self._clean_abstract(abstract)
        
        return None
    
    async def fetch_abstract_from_semantic_scholar(self, doi: str) -> Optional[str]:
        """
        异步从Semantic Scholar API获取论文摘要
        
        Args:
            doi: 论文DOI
            
        Returns:
            论文摘要文本，如果获取失败返回None
        """
        url = f"{self.semantic_scholar_base_url}{doi}"
        data = await self._request_with_retry_async(url, doi, "Semantic Scholar")
        
        if data is None:
            return None
        
        # Semantic Scholar返回的摘要在abstract字段中
        abstract = data.get('abstract', '')
        
        if abstract:
            return self._clean_abstract(abstract)
        
        return None
    
    async def fetch_abstract_by_doi_async(self, doi: str) -> Optional[str]:
        """
        异步使用多个API源获取摘要，具有回退机制
        
        Args:
            doi: 论文DOI
            
        Returns:
            论文摘要文本，如果所有API都获取失败返回None
        """
        # 1. 尝试OpenAlex API
        abstract = await self.fetch_abstract_from_openalex(doi)
        if abstract:
            return abstract

        # 2. 尝试Semantic Scholar API
        abstract = await self.fetch_abstract_from_semantic_scholar(doi)
        if abstract:
            return abstract

        # 3. 尝试CrossRef API
        abstract = await self.fetch_abstract_from_crossref(doi)
        if abstract:
            return abstract
        
        return None
    
    # ========================== 多级别处理论文 ==========================

    async def process_paper_async(self, paper: dict, json_file: Path) -> bool:
        """
        异步处理单篇论文的摘要获取
        
        Args:
            paper: 论文数据字典
            json_file: JSON文件路径
            
        Returns:
            是否成功获取到摘要
        """
        # 1. 处理文件信息
        if json_file not in self.stats_map:
            self.stats_map[json_file] = {key: 0 for key in self.stats_keys}

        self.stats_map[json_file]['total_papers'] += 1
        
        # 2. 信息检查
        # 检查是否有DOI
        doi = paper.get('doi', '')
        if not doi:
            self.stats_map[json_file]['papers_without_doi'] += 1
            return False

        # 检查是否已有摘要
        if 'abstract' in paper and paper['abstract']:
            self.stats_map[json_file]['papers_with_abstract'] += 1
            return False
        
        # 3. 通过多个API源获取摘要（具有回退机制）
        abstract = await self.fetch_abstract_by_doi_async(doi)
        if abstract:
            paper['abstract'] = abstract
            self.stats_map[json_file]['papers_abstract_fetched'] += 1
            return True
        else:
            self.stats_map[json_file]['papers_abstract_failed'] += 1
            return False
    
    async def process_papers_async(self, papers: List[dict], json_file: Path) -> Tuple[int, int]:
        """
        异步并发处理一批论文的摘要获取
        
        Args:
            papers: 论文数据列表
            json_file: JSON文件路径
            
        Returns:
            (成功处理的论文数, 总论文数)
        """
        # 初始化统计信息
        if json_file not in self.stats_map:
            self.stats_map[json_file] = {key: 0 for key in self.stats_keys}
        self.stats_map[json_file]['total_papers'] += len(papers)
        
        # 过滤出需要处理的论文
        papers_to_process = []
        for paper in papers:
            doi = paper.get('doi', '')
            if not doi:
                self.stats_map[json_file]['papers_without_doi'] += 1
                continue
                
            if 'abstract' in paper and paper['abstract']:
                self.stats_map[json_file]['papers_with_abstract'] += 1
                continue
                
            papers_to_process.append(paper)
        
        if not papers_to_process:
            return 0, len(papers)
        
        # 并发处理所有论文
        tasks = [self.process_paper_async(paper, json_file) for paper in papers_to_process]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计成功数量
        successful_count = sum(1 for result in results if result is True)
        
        return successful_count, len(papers)
    
    async def process_file_async(self, json_file: Path) -> bool:
        """
        异步处理单个JSON文件
        
        Args:
            json_file: JSON文件路径
            
        Returns:
            文件是否被更新
        """
        try:
            # 读取JSON文件
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            papers = data.get('papers', [])
            
            # 使用异步处理
            successful_count, total_count = await self.process_papers_async(papers, json_file)
            file_updated = successful_count > 0
            
            logging.info(f"📊 文件 {json_file.name}: 成功处理 {successful_count}/{total_count} 篇论文")
            
            # 如果文件有更新，保存回去
            if file_updated:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            
            return file_updated
            
        except Exception as e:
            logging.error(f"❌ 处理文件失败: {json_file.name} - {e}")
            return False
    
    async def process_dir_async(self, data_dir: str):
        """
        异步执行摘要获取流程
        """
        # 获取所有JSON文件
        json_files = list(Path(data_dir).glob("*.json"))
        
        if not json_files:
            logging.error(f"❌ 目录中没有找到JSON文件: {data_dir}")
            return
        
        logging.info(f"📁 开始异步处理目录: {data_dir}: {len(json_files)} 文件数")
        
        # 并发处理所有文件
        tasks = [self.process_file_async(json_file) for json_file in sorted(json_files)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计处理结果
        successful_files = sum(1 for result in results if result is True)
        logging.info(f"✅ 成功处理 {successful_files}/{len(json_files)} 个文件")
        
        # 输出统计信息
        self.print_stats()
    
    def print_stats(self):
        """
        打印统计信息
        """
        logging.info("=" * 80)
        logging.info("📊 异步摘要获取统计:")
        
        total_stats = {key: 0 for key in self.stats_keys}
        for file_stats in self.stats_map.values():
            for key in self.stats_keys:
                total_stats[key] += file_stats.get(key, 0)
        
        logging.info(f"📄 总论文数: {total_stats['total_papers']:,}")
        logging.info(f"✅ 已有摘要: {total_stats['papers_with_abstract']:,}")
        logging.info(f"❌ 无DOI信息: {total_stats['papers_without_doi']:,}")
        logging.info(f"🆕 新获取摘要: {total_stats['papers_abstract_fetched']:,}")
        logging.info(f"⚠️ 获取失败: {total_stats['papers_abstract_failed']:,}")
        logging.info("=" * 80)


def main_papers_meta(data_dir: str):

    # 配置需要处理的会议
    # venues = ['iclr']
    venues = get_all_venue_by_rule('a', 'conf')
    # 没问题的会议
    SKIP_CONFS = [
        'sc', # 搜索结果过多
        'fse_esec', # 合并拆分内容过多，不统一
        'pldi', # 混到了其他期刊中
        'popl', # 和 pldi 混合了
        'ooplsa', # 检索为空
        'vr', # x 检索即失败
        'vis', # x 数量对不上，应该 120 结果 53 篇
        'cscw', # 搜索数量极少，不对劲，正常接收了 2235 篇
        'ubicomp', # 搜索数量极少，不对劲，正常接收了 764 篇
    ]

    # 3. 开始获取
    fetcher = DBLPPaperFetcher(data_dir=data_dir)
    years = [i for i in range(2010, 2025)]
    for year in years:
        for venue in venues:
            if venue in SKIP_CONFS: continue
            filepath = fetcher.get_papers_by_venue_and_year(venue, year)
            time.sleep(0.5)

def main_infos(data_dir: str):
    info = DBLPPaperInfo()
    info.info_by_dir(data_dir)

async def main_papers_abstract(data_dir: str, max_concurrent: int = 100):
    """
    使用异步处理获取论文摘要的主函数
    
    Args:
        data_dir: 数据目录路径
        max_concurrent: 最大并发请求数
    """
    async with AsyncAbstractFetcher(max_concurrent) as fetcher:
        await fetcher.process_dir_async(data_dir)

async def test_doi_abstract_fetcher():
    """测试异步摘要获取器，使用 test_dois.json 中的DOI进行批量测试"""
    # 加载测试DOI
    with open('test_dois.json', 'r', encoding='utf-8') as f:
        doi_list = json.load(f)

    # 每个会议的测试结果
    test_results = { venue: { 'total': 0, 'successful': 0, 'failed': 0 } for venue in doi_list.keys() }

    async with AsyncAbstractFetcher(max_concurrent=30) as fetcher:
        # 遍历不同会议
        for venue, dois in doi_list.items():
            total = len(dois)
            test_results[venue]['total'] = total
            if total == 0:
                print(f"⚠️ {venue}: 无DOI可测试")
                continue

            async def fetch_one(d):
                try:
                    abstract = await fetcher.fetch_abstract_by_doi_async(d)
                    return 1 if abstract else 0
                except Exception:
                    return 0

            tasks = [asyncio.create_task(fetch_one(d)) for d in dois]

            from tqdm import tqdm
            pbar = tqdm(total=total, desc=f"Async Test {venue} ({total} DOIs)", unit=" DOI", leave=False)
            successful = 0
            for coro in asyncio.as_completed(tasks):
                res = await coro
                successful += res
                pbar.update(1)
            pbar.close()

            failed = total - successful
            test_results[venue]['successful'] = successful
            test_results[venue]['failed'] = failed

            success_rate = (successful / total) * 100 if total else 0.0
            start_icon = "✅" if success_rate == 100 else "⚠️"
            print(f"{start_icon} {venue}: 总数 {total}, 成功率 {success_rate:.1f}%")

    # 输出汇总后的结果（不是一个一个打印，而是输出一个最终所有会议失败成功等数量加起来后的数据）
    total_successful = sum(result['successful'] for result in test_results.values())
    total_failed = sum(result['failed'] for result in test_results.values())
    total_total = sum(result['total'] for result in test_results.values())
    success_rate = (total_successful / total_total) * 100 if total_total else 0.0
    print(f"汇总结果: 总数 {total_total}, 成功率 {success_rate:.1f}%，成功 {total_successful}，失败 {total_failed}")

async def test_urls_abstract_fetcher():
    """测试异步摘要获取器，使用 test_dois.json 中的DOI进行批量测试"""
    # 加载测试DOI
    with open('test_urls.json', 'r', encoding='utf-8') as f:
        urls_list = json.load(f)

    # 每个会议的测试结果
    test_results = { venue: { 'total': 0, 'successful': 0, 'failed': 0 } for venue in urls_list.keys() }

    async with AsyncAbstractFetcher(max_concurrent=30) as fetcher:
        # 遍历不同会议
        for venue, urls in urls_list.items():
            total = len(urls)
            test_results[venue]['total'] = total
            if total == 0:
                print(f"⚠️ {venue}: 无URL可测试")
                continue

            async def fetch_one(url):
                try:
                    abstract = await fetcher.fetch_abstract_by_url_async(url)
                    return 1 if abstract else 0
                except Exception:
                    return 0

            tasks = [asyncio.create_task(fetch_one(url)) for url in urls]

            from tqdm import tqdm
            pbar = tqdm(total=total, desc=f"Async Test {venue} ({total} URLs)", unit=" URL", leave=False)
            successful = 0
            for coro in asyncio.as_completed(tasks):
                res = await coro
                successful += res
                pbar.update(1)
            pbar.close()

            failed = total - successful
            test_results[venue]['successful'] = successful
            test_results[venue]['failed'] = failed

            success_rate = (successful / total) * 100 if total else 0.0
            start_icon = "✅" if success_rate == 100 else "⚠️"
            print(f"{start_icon} {venue}: 总数 {total}, 成功率 {success_rate:.1f}%")

    # 输出汇总后的结果（不是一个一个打印，而是输出一个最终所有会议失败成功等数量加起来后的数据）
    total_successful = sum(result['successful'] for result in test_results.values())
    total_failed = sum(result['failed'] for result in test_results.values())
    total_total = sum(result['total'] for result in test_results.values())
    success_rate = (total_successful / total_total) * 100 if total_total else 0.0
    print(f"汇总结果: 总数 {total_total}, 成功率 {success_rate:.1f}%，成功 {total_successful}，失败 {total_failed}")

async def test_url_abstract_fetcher():
    """测试异步摘要获取器，使用 test_dois.json 中的DOI进行批量测试"""
    urls = [
        "https://dl.acm.org/citation.cfm?id=2093503",
        "https://dl.acm.org/citation.cfm?id=2093490",
        "https://dl.acm.org/citation.cfm?id=2093510",
        "https://dl.acm.org/citation.cfm?id=2093504",
        "https://dl.acm.org/citation.cfm?id=2093505",
        "https://dl.acm.org/citation.cfm?id=2093514",
        "https://dl.acm.org/citation.cfm?id=2093483",
        "https://dl.acm.org/citation.cfm?id=2093502",
        "https://dl.acm.org/citation.cfm?id=2093515",
        "https://dl.acm.org/citation.cfm?id=2093495",
        "https://dl.acm.org/citation.cfm?id=2093507",
        "https://dl.acm.org/citation.cfm?id=2093486",
        "https://dl.acm.org/citation.cfm?id=2093479",
        "https://dl.acm.org/citation.cfm?id=2093499",
        "https://dl.acm.org/citation.cfm?id=2093513",
        "https://dl.acm.org/citation.cfm?id=2093480",
    ]
    async with AsyncAbstractFetcher(max_concurrent=10) as fetcher:
        for url in urls:
            abstract = await fetcher.fetch_abstract_by_url_async(url)
            print(url, abstract)

if __name__ == "__main__":
    # 1. 定义保存目录
    data_dir = os.path.join(ROOT_DIR, 'data', 'paper', 'conf_a')
    log_dir = os.path.join(ROOT_DIR, 'data', 'logs')

    # 2. 配置日志
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(os.path.join(log_dir, f'log_{int(time.time())}.txt'), mode='w', encoding='utf-8')
        ]
    )
    
    # 3. 获取论文元信息
    # main_papers_meta(data_dir)

    # 4. 查看获取后的信息统计
    # main_infos(data_dir)

    # 5. 获取论文摘要（异步版本 - 推荐）
    # asyncio.run(main_papers_abstract_async(data_dir, max_concurrent=100))

    # ================================== 测试 ==================================

    # 6. 测试异步摘要获取器
    # asyncio.run(test_doi_abstract_fetcher())
    asyncio.run(test_url_abstract_fetcher())