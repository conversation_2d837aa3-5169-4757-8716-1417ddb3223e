#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查会议论文中缺失DOI信息的脚本
找出 data/paper/conf_a_old 路径下所有会议，有哪些会议对应的 paper 信息下不包含 doi 这个 key
"""

import os
import json
import glob
from pathlib import Path

def check_conference_doi_status():
    """
    检查所有会议文件的DOI状态
    """
    # 会议文件路径
    conf_path = Path("data/paper/conf_a_old")
    
    if not conf_path.exists():
        print(f"错误：路径 {conf_path} 不存在")
        return
    
    # 获取所有JSON文件
    json_files = glob.glob(str(conf_path / "*.json"))
    
    if not json_files:
        print(f"在 {conf_path} 路径下没有找到JSON文件")
        return
    
    print(f"找到 {len(json_files)} 个会议文件")
    print("=" * 60)
    
    # 存储结果
    conferences_without_doi = []
    conferences_with_doi = []
    total_papers = 0
    papers_without_doi = 0
    
    for json_file in sorted(json_files):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 获取会议名称和年份
            venue_name = data.get('metadata', {}).get('venue_name', 'unknown')
            year = data.get('metadata', {}).get('year', 'unknown')
            conference_name = f"{venue_name}_{year}"
            
            # 检查论文列表
            papers = data.get('papers', [])
            if not papers:
                print(f"警告：{conference_name} 没有论文数据")
                continue
            
            # 统计DOI状态
            papers_with_doi = 0
            papers_missing_doi = 0
            
            for paper in papers:
                total_papers += 1
                if 'doi' in paper and paper['doi']:
                    papers_with_doi += 1
                else:
                    papers_missing_doi += 1
                    papers_without_doi += 1
            
            # 判断会议是否完全缺失DOI
            if papers_missing_doi == len(papers):
                conferences_without_doi.append({
                    'name': conference_name,
                    'total_papers': len(papers),
                    'papers_without_doi': papers_missing_doi
                })
                print(f"❌ {conference_name}: {papers_missing_doi}/{len(papers)} 论文缺失DOI")
            else:
                conferences_with_doi.append({
                    'name': conference_name,
                    'total_papers': len(papers),
                    'papers_with_doi': papers_with_doi,
                    'papers_without_doi': papers_missing_doi
                })
                if papers_missing_doi > 0:
                    print(f"⚠️  {conference_name}: {papers_missing_doi}/{len(papers)} 论文缺失DOI")
                else:
                    print(f"✅ {conference_name}: 所有论文都有DOI")
                    
        except Exception as e:
            print(f"错误：处理文件 {json_file} 时出错: {e}")
    
    print("=" * 60)
    print("统计结果：")
    print(f"总会议数: {len(json_files)}")
    print(f"完全缺失DOI的会议数: {len(conferences_without_doi)}")
    print(f"部分缺失DOI的会议数: {len([c for c in conferences_with_doi if c['papers_without_doi'] > 0])}")
    print(f"总论文数: {total_papers}")
    print(f"缺失DOI的论文数: {papers_without_doi}")
    
    if conferences_without_doi:
        print("\n完全缺失DOI的会议列表：")
        print("-" * 40)
        for conf in conferences_without_doi:
            print(f"• {conf['name']} ({conf['total_papers']} 篇论文)")
    
    # 显示部分缺失DOI的会议
    partial_missing = [c for c in conferences_with_doi if c['papers_without_doi'] > 0]
    if partial_missing:
        print("\n部分缺失DOI的会议列表：")
        print("-" * 40)
        for conf in partial_missing:
            print(f"• {conf['name']}: {conf['papers_without_doi']}/{conf['total_papers']} 论文缺失DOI")

if __name__ == "__main__":
    check_conference_doi_status()
