#!/usr/bin/env python3
"""
从conf_a_old文件夹中提取2021-2023年的DOI，每个会议随机挑选10个
"""

import json
import os
import random
from pathlib import Path
from collections import defaultdict

def extract_recent_dois(num_dois: int = 10):
    """从conf_a_old文件夹中提取2021-2023年的DOI"""
    
    # 会议文件夹路径
    conf_dir = Path("data/paper/conf_a_old")
    
    # 存储每个会议的DOI
    venue_dois = defaultdict(list)
    venue_urls = defaultdict(list)
    
    # 遍历所有JSON文件
    for json_file in conf_dir.glob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 获取会议名称和年份
            metadata = data.get('metadata', {})
            venue_name = metadata.get('venue_name', '')
            year = metadata.get('year', '')
            # if not venue_name or year not in [2021, 2022, 2023, 2024, 2025]:
            #     continue
            
            # 从papers中提取DOI
            papers = data.get('papers', [])

            dois = []
            urls = []

            for paper in papers:
                # 跳过编辑类论文
                if paper.get('type', '') == 'Editorship':
                    continue
                
                # 提取 doi
                doi = paper.get('doi', '')
                if doi and doi.strip():
                    dois.append(doi.strip())
                    continue

                # 跳过特殊会议
                if venue_name == 'icse': continue # 无 doi 的几篇为编辑类论文
                if venue_name == 'sosp': continue # 这几个 url 访问页面为空
                if venue_name == 'wine': continue # url 完全相同，无意义
                if venue_name == 'aaai': continue # 只有一个 url 对象，并且无意义
                if venue_name == 'dac': continue # 只有一个 url 对象，并且无意义

                # 提取 url
                url = paper.get('ee', [''])[0]
                if url and url.strip():
                    urls.append(url.strip())
            
            # 随机选择10个DOI（如果不足10个则全部选择）
            if dois:
                selected_dois = random.sample(dois, min(num_dois, len(dois)))
                venue_dois[venue_name.lower()] = selected_dois
                print(f"✅ {venue_name} ({year}): 找到 {len(dois)} 个DOI，选择 {len(selected_dois)} 个")
            else:
                print(f"⚠️ {venue_name} ({year}): 没有找到DOI")

            # 随机选择10个URL（如果不足10个则全部选择）
            if urls:
                selected_urls = random.sample(urls, min(num_dois, len(urls)))
                venue_urls[venue_name.lower()] = selected_urls
                print(f"✅ {venue_name} ({year}): 找到 {len(urls)} 个URL，选择 {len(selected_urls)} 个")
            else:
                print(f"⚠️ {venue_name} ({year}): 没有找到URL")
                
        except Exception as e:
            print(f"❌ 处理文件失败: {json_file.name} - {e}")
    
    # 保存到JSON文件
    output_file = "test_dois.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(venue_dois, f, ensure_ascii=False, indent=2)

    output_file = "test_urls.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(venue_urls, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 统计信息:")
    print(f"总共处理了 {len(venue_dois)} 个会议")
    total_dois = sum(len(dois) for dois in venue_dois.values())
    print(f"总共选择了 {total_dois} 个DOI")
    total_urls = sum(len(urls) for urls in venue_urls.values())
    print(f"总共选择了 {total_urls} 个URL")
    print(f"结果已保存到: {output_file}")
    
    return venue_dois

if __name__ == "__main__":
    # 设置随机种子以确保结果可重现
    random.seed(42)
    
    print("🔍 开始从conf_a_old文件夹提取2021-2023年的DOI...")
    extract_recent_dois(num_dois=40)
    print("✅ 提取完成！")
